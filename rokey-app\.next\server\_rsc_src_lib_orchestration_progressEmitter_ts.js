"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_orchestration_progressEmitter_ts";
exports.ids = ["_rsc_src_lib_orchestration_progressEmitter_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/orchestration/progressEmitter.ts":
/*!**************************************************!*\
  !*** ./src/lib/orchestration/progressEmitter.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! events */ \"events\");\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(events__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * RouKey Orchestration Progress Emitter\n * \n * Captures orchestration progress events and emits them to the frontend\n * for real-time status updates with proper RouKey branding.\n */ \nclass OrchestrationProgressEmitter extends events__WEBPACK_IMPORTED_MODULE_0__.EventEmitter {\n    constructor(){\n        super(), this.currentColorIndex = 0, this.colors = [\n            'blue',\n            'purple',\n            'indigo',\n            'cyan',\n            'teal',\n            'green',\n            'yellow',\n            'orange',\n            'emerald',\n            'red' // Error\n        ];\n    }\n    static getInstance() {\n        if (!OrchestrationProgressEmitter.instance) {\n            OrchestrationProgressEmitter.instance = new OrchestrationProgressEmitter();\n        }\n        return OrchestrationProgressEmitter.instance;\n    }\n    getNextColor() {\n        const color = this.colors[this.currentColorIndex % this.colors.length];\n        this.currentColorIndex++;\n        return color;\n    }\n    emitProgress(type, message, data) {\n        const event = {\n            id: crypto.randomUUID(),\n            timestamp: new Date().toISOString(),\n            type,\n            message,\n            data,\n            colorIndex: this.currentColorIndex\n        };\n        // Increment color index for visual differentiation\n        this.currentColorIndex++;\n        console.log(`[Progress Emitter] ${message}`);\n        this.emit('progress', event);\n    }\n    // Convenience methods for different progress types\n    classificationStart() {\n        this.emitProgress('classification_start', '🔍 Multi-role task detected by RouKey\\'s Classifier');\n    }\n    classificationComplete(roles, threshold) {\n        this.emitProgress('classification_complete', `✅ Detected ${roles.length} roles: ${roles.join(', ')}`, {\n            roles,\n            threshold\n        });\n    }\n    roleSelectionComplete(selectedRoles, filteredRoles) {\n        this.emitProgress('role_selection', `🎯 Selected ${selectedRoles.length} roles for orchestration`, {\n            selectedRoles,\n            filteredRoles\n        });\n    }\n    workflowSelectionComplete(workflowType, reasoning) {\n        this.emitProgress('workflow_selection', `🏗️ RouKey Multi-Role System selected ${workflowType} workflow`, {\n            workflowType,\n            reasoning\n        });\n    }\n    agentCreationStart() {\n        this.emitProgress('agent_creation_start', '🤖 Creating specialized agents...');\n    }\n    agentCreationComplete(agents) {\n        this.emitProgress('agent_creation_complete', `✅ Created ${agents.length} specialized agents`, {\n            agents\n        });\n    }\n    supervisorInitStart() {\n        this.emitProgress('supervisor_init', '👑 Initializing supervisor coordination...');\n    }\n    supervisorInitComplete(supervisorRole) {\n        this.emitProgress('supervisor_init', '✅ Supervisor ready for coordination', {\n            supervisorRole\n        });\n    }\n    taskPlanningStart() {\n        this.emitProgress('task_planning', '📋 Planning task distribution...');\n    }\n    taskPlanningComplete(plan) {\n        this.emitProgress('task_planning', '✅ Task distribution planned', {\n            plan\n        });\n    }\n    agentWorkStart(role, task) {\n        this.emitProgress('agent_work_start', `🚀 ${role} agent starting work...`, {\n            role,\n            task\n        });\n    }\n    agentWorkComplete(role, result) {\n        this.emitProgress('agent_work_complete', `✅ ${role} agent completed work`, {\n            role,\n            result\n        });\n    }\n    supervisorSynthesisStart() {\n        this.emitProgress('supervisor_synthesis', '🔄 Supervisor synthesizing results...');\n    }\n    supervisorSynthesisComplete(synthesis) {\n        this.emitProgress('supervisor_synthesis', '✅ Final synthesis complete', {\n            synthesis\n        });\n    }\n    orchestrationComplete(result) {\n        this.emitProgress('orchestration_complete', '🎉 RouKey Multi-Role orchestration complete!', {\n            result\n        });\n    }\n    error(step, error) {\n        this.emitProgress('error', `❌ Error in ${step}: ${error}`, {\n            step,\n            error\n        });\n    }\n    // Reset color index for new orchestration sessions\n    resetColorIndex() {\n        this.currentColorIndex = 0;\n    }\n    // Get current color for UI\n    getCurrentColor() {\n        return this.colors[(this.currentColorIndex - 1) % this.colors.length] || 'blue';\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrchestrationProgressEmitter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/orchestration/progressEmitter.ts\n");

/***/ })

};
;