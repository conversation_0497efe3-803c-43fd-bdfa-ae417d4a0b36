// Debug package fallback for browser environments
// This prevents the webpack error when debug package is imported

function createDebug() {
  function debug() {
    // No-op in browser
  }
  
  debug.enabled = false;
  debug.namespace = '';
  debug.extend = () => debug;
  debug.destroy = () => {};
  debug.log = () => {};
  
  return debug;
}

// Export both default and named exports to match debug package API
const debug = createDebug();
debug.default = debug;

module.exports = debug;
module.exports.default = debug;
