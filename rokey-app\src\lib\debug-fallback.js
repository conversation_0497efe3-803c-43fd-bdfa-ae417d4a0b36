// Debug package fallback for browser environments
// This prevents the webpack error when debug package is imported

function createDebug() {
  function debug() {
    // No-op in browser
  }

  debug.enabled = false;
  debug.namespace = '';
  debug.extend = () => debug;
  debug.destroy = () => {};
  debug.log = () => {};
  debug.call = () => {}; // Add the missing call property

  return debug;
}

// Export both default and named exports to match debug package API
const debug = createDebug();
debug.default = debug;
debug.call = () => {}; // Add call property to the main debug object too

module.exports = debug;
module.exports.default = debug;
