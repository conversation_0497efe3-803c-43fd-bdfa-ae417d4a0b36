/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/cleanup-semantic-cache/route";
exports.ids = ["app/api/admin/cleanup-semantic-cache/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&page=%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&page=%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_admin_cleanup_semantic_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/cleanup-semantic-cache/route.ts */ \"(rsc)/./src/app/api/admin/cleanup-semantic-cache/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/cleanup-semantic-cache/route\",\n        pathname: \"/api/admin/cleanup-semantic-cache\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/cleanup-semantic-cache/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\admin\\\\cleanup-semantic-cache\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_admin_cleanup_semantic_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRmNsZWFudXAtc2VtYW50aWMtY2FjaGUlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFkbWluJTJGY2xlYW51cC1zZW1hbnRpYy1jYWNoZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFkbWluJTJGY2xlYW51cC1zZW1hbnRpYy1jYWNoZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUm9LZXklMjBBcHAlNUNyb2tleS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNSb0tleSUyMEFwcCU1Q3Jva2V5LWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDZ0M7QUFDN0c7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYWRtaW5cXFxcY2xlYW51cC1zZW1hbnRpYy1jYWNoZVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYWRtaW4vY2xlYW51cC1zZW1hbnRpYy1jYWNoZS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FkbWluL2NsZWFudXAtc2VtYW50aWMtY2FjaGVcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2FkbWluL2NsZWFudXAtc2VtYW50aWMtY2FjaGUvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxSb0tleSBBcHBcXFxccm9rZXktYXBwXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFkbWluXFxcXGNsZWFudXAtc2VtYW50aWMtY2FjaGVcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&page=%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/cleanup-semantic-cache/route.ts":
/*!***********************************************************!*\
  !*** ./src/app/api/admin/cleanup-semantic-cache/route.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Admin endpoint to clean up invalid semantic cache entries\n * This removes cache entries that have invalid response structures like { note: \"streamed\" }\n */ \n\n// Create service role client for admin operations\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\nasync function POST(request) {\n    try {\n        // Simple admin authentication - check for admin key\n        const adminKey = request.headers.get('x-admin-key');\n        const expectedAdminKey = process.env.ROKEY_ADMIN_KEY || 'rokey-semantic-cache-cleanup-2024';\n        if (adminKey !== expectedAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const supabase = createServiceRoleClient();\n        // Find cache entries with invalid response structures\n        const { data: invalidEntries, error: fetchError } = await supabase.from('semantic_cache').select('id, response_data').not('response_data', 'is', null);\n        if (fetchError) {\n            console.error('[Cache Cleanup] Error fetching cache entries:', fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch cache entries'\n            }, {\n                status: 500\n            });\n        }\n        if (!invalidEntries || invalidEntries.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'No cache entries found',\n                deletedCount: 0\n            });\n        }\n        // Filter entries with invalid structures\n        const entriesToDelete = invalidEntries.filter((entry)=>{\n            const responseData = entry.response_data;\n            // Check for invalid structures\n            if (responseData && typeof responseData === 'object') {\n                // Invalid: { note: \"streamed\" }\n                if (responseData.note === 'streamed' && Object.keys(responseData).length === 1) {\n                    return true;\n                }\n                // Invalid: Missing expected OpenAI structure\n                if (!responseData.choices && !responseData.content && !responseData.message) {\n                    return true;\n                }\n            }\n            return false;\n        });\n        console.log(`[Cache Cleanup] Found ${entriesToDelete.length} invalid entries out of ${invalidEntries.length} total`);\n        if (entriesToDelete.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'No invalid cache entries found',\n                deletedCount: 0,\n                totalEntries: invalidEntries.length\n            });\n        }\n        // Delete invalid entries\n        const idsToDelete = entriesToDelete.map((entry)=>entry.id);\n        const { error: deleteError } = await supabase.from('semantic_cache').delete().in('id', idsToDelete);\n        if (deleteError) {\n            console.error('[Cache Cleanup] Error deleting invalid entries:', deleteError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete invalid entries'\n            }, {\n                status: 500\n            });\n        }\n        console.log(`[Cache Cleanup] ✅ Deleted ${entriesToDelete.length} invalid cache entries`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Cleaned up ${entriesToDelete.length} invalid cache entries`,\n            deletedCount: entriesToDelete.length,\n            totalEntries: invalidEntries.length,\n            remainingEntries: invalidEntries.length - entriesToDelete.length\n        });\n    } catch (error) {\n        console.error('[Cache Cleanup] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Semantic Cache Cleanup Endpoint',\n        usage: {\n            'POST /api/admin/cleanup-semantic-cache': {\n                description: 'Clean up invalid semantic cache entries',\n                headers: {\n                    'x-admin-key': 'Required admin key for authentication'\n                },\n                example: 'curl -X POST /api/admin/cleanup-semantic-cache -H \"x-admin-key: your-admin-key\"'\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/cleanup-semantic-cache/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&page=%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup-semantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();