"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_MarkdownRenderer_tsx"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcYXBwLWR5bmFtaWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vc2hhcmVkL2xpYi9hcHAtZHluYW1pYyc7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9hcHAtZHluYW1pYyc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1keW5hbWljLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    const loadableOptions = {};\n    if (typeof dynamicOptions === 'function') {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (false) {}\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQWNnQkE7OztlQUFBQTs7OzBDQVhrQjtBQVczQixzQkFBc0IsS0FBdUM7SUFBdkMsTUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQXFCLEdBQXZDO0lBQzNCLElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUVELE9BQU9BO0FBQ1Q7S0FOZ0JGIiwic291cmNlcyI6WyJDOlxcc3JjXFxzaGFyZWRcXGxpYlxcbGF6eS1keW5hbWljXFxkeW5hbWljLWJhaWxvdXQtdG8tY3NyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJhaWxvdXRUb0NTUkVycm9yIH0gZnJvbSAnLi9iYWlsb3V0LXRvLWNzcidcblxuaW50ZXJmYWNlIEJhaWxvdXRUb0NTUlByb3BzIHtcbiAgcmVhc29uOiBzdHJpbmdcbiAgY2hpbGRyZW46IFJlYWN0RWxlbWVudFxufVxuXG4vKipcbiAqIElmIHJlbmRlcmVkIG9uIHRoZSBzZXJ2ZXIsIHRoaXMgY29tcG9uZW50IHRocm93cyBhbiBlcnJvclxuICogdG8gc2lnbmFsIE5leHQuanMgdGhhdCBpdCBzaG91bGQgYmFpbCBvdXQgdG8gY2xpZW50LXNpZGUgcmVuZGVyaW5nIGluc3RlYWQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBCYWlsb3V0VG9DU1IoeyByZWFzb24sIGNoaWxkcmVuIH06IEJhaWxvdXRUb0NTUlByb3BzKSB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xuICAgIHRocm93IG5ldyBCYWlsb3V0VG9DU1JFcnJvcihyZWFzb24pXG4gIH1cblxuICByZXR1cm4gY2hpbGRyZW5cbn1cbiJdLCJuYW1lcyI6WyJCYWlsb3V0VG9DU1IiLCJyZWFzb24iLCJjaGlsZHJlbiIsIndpbmRvdyIsIkJhaWxvdXRUb0NTUkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadchunks = __webpack_require__(/*! ./preload-chunks */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && 'default' in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n        const hasSuspenseBoundary = !opts.ssr || !!opts.loading;\n        const Wrap = hasSuspenseBoundary ? _react.Suspense : _react.Fragment;\n        const wrapProps = hasSuspenseBoundary ? {\n            fallback: fallbackElement\n        } : {};\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                 false ? /*#__PURE__*/ 0 : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Wrap, {\n            ...wrapProps,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = 'LoadableComponent';\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvbG9hZGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OzsyQ0E0RUE7OztlQUFBOzs7O21DQTVFeUM7aURBQ1o7MkNBRUM7QUFFOUIseUZBQXlGO0FBQ3pGLHFHQUFxRztBQUNyRyxxRUFBcUU7QUFDckUsU0FBU0EsY0FDUEMsR0FBNEQ7SUFJNUQsaUhBQWlIO0lBQ2pILFNBQVM7SUFDVCw4QkFBOEI7SUFDOUIsaUJBQWlCO0lBQ2pCLHFDQUFxQztJQUNyQyx3QkFBd0I7SUFDeEIsTUFBTUMsYUFBYUQsT0FBTyxhQUFhQTtJQUN2QyxPQUFPO1FBQ0xFLFNBQVNELGFBQ0pELElBQTJCRSxPQUFPLEdBQ2xDRjtJQUNQO0FBQ0Y7QUFFQSxNQUFNRyxpQkFBaUI7SUFDckJDLFFBQVEsSUFBTUMsUUFBUUMsT0FBTyxDQUFDUCxjQUFjLElBQU07SUFDbERRLFNBQVM7SUFDVEMsS0FBSztBQUNQO0FBU0Esa0JBQWtCRSxPQUF3QjtJQUN4QyxNQUFNQyxPQUFPO1FBQUUsR0FBR1IsY0FBYztRQUFFLEdBQUdPLE9BQU87SUFBQztJQUM3QyxNQUFNRSxPQUFBQSxXQUFBQSxHQUFPQyxDQUFBQSxHQUFBQSxPQUFBQSxJQUFBQSxFQUFLLElBQU1GLEtBQUtQLE1BQU0sR0FBR1UsSUFBSSxDQUFDZjtJQUMzQyxNQUFNZ0IsVUFBVUosS0FBS0osT0FBTztJQUU1QixTQUFTUyxrQkFBa0JDLEtBQVU7UUFDbkMsTUFBTUMsa0JBQWtCSCxVQUFBQSxXQUFBQSxHQUN0QixxQkFBQ0EsU0FBQUE7WUFBUUksV0FBVztZQUFNQyxXQUFXO1lBQU1DLE9BQU87YUFDaEQ7UUFFSixrRkFBa0Y7UUFDbEYsTUFBTUMsc0JBQXNCLENBQUNYLEtBQUtILEdBQUcsSUFBSSxDQUFDLENBQUNHLEtBQUtKLE9BQU87UUFDdkQsTUFBTWdCLE9BQU9ELHNCQUFzQkUsT0FBQUEsUUFBUSxHQUFHQyxPQUFBQSxRQUFRO1FBQ3RELE1BQU1DLFlBQVlKLHNCQUFzQjtZQUFFSyxVQUFVVDtRQUFnQixJQUFJLENBQUM7UUFDekUsTUFBTVUsV0FBV2pCLEtBQUtILEdBQUcsaUJBQ3ZCOztnQkFFRyxNQUE2QixHQUFYLGNBQ2pCLEMsR0FDRTs4QkFDSixxQkFBQ0ksTUFBQUE7b0JBQU0sR0FBR0ssS0FBSzs7O2FBR2pCLG1DQUFDZ0IscUJBQUFBLFlBQVk7WUFBQ0MsUUFBTztzQkFDbkIsbUNBQUN0QixNQUFBQTtnQkFBTSxHQUFHSyxLQUFLOzs7UUFJbkIscUJBQU8scUJBQUNNLE1BQUFBO1lBQU0sR0FBR0csU0FBUztzQkFBR0U7O0lBQy9CO0lBRUFaLGtCQUFrQm1CLFdBQVcsR0FBRztJQUVoQyxPQUFPbkI7QUFDVDtLQWxDU1A7TUFvQ1QsV0FBZUEiLCJzb3VyY2VzIjpbIkM6XFxzcmNcXHNoYXJlZFxcbGliXFxsYXp5LWR5bmFtaWNcXGxvYWRhYmxlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdXNwZW5zZSwgRnJhZ21lbnQsIGxhenkgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJhaWxvdXRUb0NTUiB9IGZyb20gJy4vZHluYW1pYy1iYWlsb3V0LXRvLWNzcidcbmltcG9ydCB0eXBlIHsgQ29tcG9uZW50TW9kdWxlIH0gZnJvbSAnLi90eXBlcydcbmltcG9ydCB7IFByZWxvYWRDaHVua3MgfSBmcm9tICcuL3ByZWxvYWQtY2h1bmtzJ1xuXG4vLyBOb3JtYWxpemUgbG9hZGVyIHRvIHJldHVybiB0aGUgbW9kdWxlIGFzIGZvcm0geyBkZWZhdWx0OiBDb21wb25lbnQgfSBmb3IgYFJlYWN0LmxhenlgLlxuLy8gQWxzbyBmb3IgYmFja3dhcmQgY29tcGF0aWJsZSBzaW5jZSBuZXh0L2R5bmFtaWMgYWxsb3dzIHRvIHJlc29sdmUgYSBjb21wb25lbnQgZGlyZWN0bHkgd2l0aCBsb2FkZXJcbi8vIENsaWVudCBjb21wb25lbnQgcmVmZXJlbmNlIHByb3h5IG5lZWQgdG8gYmUgY29udmVydGVkIHRvIGEgbW9kdWxlLlxuZnVuY3Rpb24gY29udmVydE1vZHVsZTxQPihcbiAgbW9kOiBSZWFjdC5Db21wb25lbnRUeXBlPFA+IHwgQ29tcG9uZW50TW9kdWxlPFA+IHwgdW5kZWZpbmVkXG4pOiB7XG4gIGRlZmF1bHQ6IFJlYWN0LkNvbXBvbmVudFR5cGU8UD5cbn0ge1xuICAvLyBDaGVjayBcImRlZmF1bHRcIiBwcm9wIGJlZm9yZSBhY2Nlc3NpbmcgaXQsIGFzIGl0IGNvdWxkIGJlIGNsaWVudCByZWZlcmVuY2UgcHJveHkgdGhhdCBjb3VsZCBicmVhayBpdCByZWZlcmVuY2UuXG4gIC8vIENhc2VzOlxuICAvLyBtb2Q6IHsgZGVmYXVsdDogQ29tcG9uZW50IH1cbiAgLy8gbW9kOiBDb21wb25lbnRcbiAgLy8gbW9kOiB7IGRlZmF1bHQ6IHByb3h5KENvbXBvbmVudCkgfVxuICAvLyBtb2Q6IHByb3h5KENvbXBvbmVudClcbiAgY29uc3QgaGFzRGVmYXVsdCA9IG1vZCAmJiAnZGVmYXVsdCcgaW4gbW9kXG4gIHJldHVybiB7XG4gICAgZGVmYXVsdDogaGFzRGVmYXVsdFxuICAgICAgPyAobW9kIGFzIENvbXBvbmVudE1vZHVsZTxQPikuZGVmYXVsdFxuICAgICAgOiAobW9kIGFzIFJlYWN0LkNvbXBvbmVudFR5cGU8UD4pLFxuICB9XG59XG5cbmNvbnN0IGRlZmF1bHRPcHRpb25zID0ge1xuICBsb2FkZXI6ICgpID0+IFByb21pc2UucmVzb2x2ZShjb252ZXJ0TW9kdWxlKCgpID0+IG51bGwpKSxcbiAgbG9hZGluZzogbnVsbCxcbiAgc3NyOiB0cnVlLFxufVxuXG5pbnRlcmZhY2UgTG9hZGFibGVPcHRpb25zIHtcbiAgbG9hZGVyPzogKCkgPT4gUHJvbWlzZTxSZWFjdC5Db21wb25lbnRUeXBlPGFueT4gfCBDb21wb25lbnRNb2R1bGU8YW55Pj5cbiAgbG9hZGluZz86IFJlYWN0LkNvbXBvbmVudFR5cGU8YW55PiB8IG51bGxcbiAgc3NyPzogYm9vbGVhblxuICBtb2R1bGVzPzogc3RyaW5nW11cbn1cblxuZnVuY3Rpb24gTG9hZGFibGUob3B0aW9uczogTG9hZGFibGVPcHRpb25zKSB7XG4gIGNvbnN0IG9wdHMgPSB7IC4uLmRlZmF1bHRPcHRpb25zLCAuLi5vcHRpb25zIH1cbiAgY29uc3QgTGF6eSA9IGxhenkoKCkgPT4gb3B0cy5sb2FkZXIoKS50aGVuKGNvbnZlcnRNb2R1bGUpKVxuICBjb25zdCBMb2FkaW5nID0gb3B0cy5sb2FkaW5nXG5cbiAgZnVuY3Rpb24gTG9hZGFibGVDb21wb25lbnQocHJvcHM6IGFueSkge1xuICAgIGNvbnN0IGZhbGxiYWNrRWxlbWVudCA9IExvYWRpbmcgPyAoXG4gICAgICA8TG9hZGluZyBpc0xvYWRpbmc9e3RydWV9IHBhc3REZWxheT17dHJ1ZX0gZXJyb3I9e251bGx9IC8+XG4gICAgKSA6IG51bGxcblxuICAgIC8vIElmIGl0J3Mgbm9uLVNTUiBvciBwcm92aWRlZCBhIGxvYWRpbmcgY29tcG9uZW50LCB3cmFwIGl0IGluIGEgc3VzcGVuc2UgYm91bmRhcnlcbiAgICBjb25zdCBoYXNTdXNwZW5zZUJvdW5kYXJ5ID0gIW9wdHMuc3NyIHx8ICEhb3B0cy5sb2FkaW5nXG4gICAgY29uc3QgV3JhcCA9IGhhc1N1c3BlbnNlQm91bmRhcnkgPyBTdXNwZW5zZSA6IEZyYWdtZW50XG4gICAgY29uc3Qgd3JhcFByb3BzID0gaGFzU3VzcGVuc2VCb3VuZGFyeSA/IHsgZmFsbGJhY2s6IGZhbGxiYWNrRWxlbWVudCB9IDoge31cbiAgICBjb25zdCBjaGlsZHJlbiA9IG9wdHMuc3NyID8gKFxuICAgICAgPD5cbiAgICAgICAgey8qIER1cmluZyBTU1IsIHdlIG5lZWQgdG8gcHJlbG9hZCB0aGUgQ1NTIGZyb20gdGhlIGR5bmFtaWMgY29tcG9uZW50IHRvIGF2b2lkIGZsYXNoIG9mIHVuc3R5bGVkIGNvbnRlbnQgKi99XG4gICAgICAgIHt0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJyA/IChcbiAgICAgICAgICA8UHJlbG9hZENodW5rcyBtb2R1bGVJZHM9e29wdHMubW9kdWxlc30gLz5cbiAgICAgICAgKSA6IG51bGx9XG4gICAgICAgIDxMYXp5IHsuLi5wcm9wc30gLz5cbiAgICAgIDwvPlxuICAgICkgOiAoXG4gICAgICA8QmFpbG91dFRvQ1NSIHJlYXNvbj1cIm5leHQvZHluYW1pY1wiPlxuICAgICAgICA8TGF6eSB7Li4ucHJvcHN9IC8+XG4gICAgICA8L0JhaWxvdXRUb0NTUj5cbiAgICApXG5cbiAgICByZXR1cm4gPFdyYXAgey4uLndyYXBQcm9wc30+e2NoaWxkcmVufTwvV3JhcD5cbiAgfVxuXG4gIExvYWRhYmxlQ29tcG9uZW50LmRpc3BsYXlOYW1lID0gJ0xvYWRhYmxlQ29tcG9uZW50J1xuXG4gIHJldHVybiBMb2FkYWJsZUNvbXBvbmVudFxufVxuXG5leHBvcnQgZGVmYXVsdCBMb2FkYWJsZVxuIl0sIm5hbWVzIjpbImNvbnZlcnRNb2R1bGUiLCJtb2QiLCJoYXNEZWZhdWx0IiwiZGVmYXVsdCIsImRlZmF1bHRPcHRpb25zIiwibG9hZGVyIiwiUHJvbWlzZSIsInJlc29sdmUiLCJsb2FkaW5nIiwic3NyIiwiTG9hZGFibGUiLCJvcHRpb25zIiwib3B0cyIsIkxhenkiLCJsYXp5IiwidGhlbiIsIkxvYWRpbmciLCJMb2FkYWJsZUNvbXBvbmVudCIsInByb3BzIiwiZmFsbGJhY2tFbGVtZW50IiwiaXNMb2FkaW5nIiwicGFzdERlbGF5IiwiZXJyb3IiLCJoYXNTdXNwZW5zZUJvdW5kYXJ5IiwiV3JhcCIsIlN1c3BlbnNlIiwiRnJhZ21lbnQiLCJ3cmFwUHJvcHMiLCJmYWxsYmFjayIsImNoaWxkcmVuIiwid2luZG93IiwiUHJlbG9hZENodW5rcyIsIm1vZHVsZUlkcyIsIm1vZHVsZXMiLCJCYWlsb3V0VG9DU1IiLCJyZWFzb24iLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadChunks\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadChunks;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../../../server/app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _encodeuripath = __webpack_require__(/*! ../encode-uri-path */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/encode-uri-path.js\");\nfunction PreloadChunks(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (true) {\n        return null;\n    }\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    if (workStore === undefined) {\n        return null;\n    }\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (workStore.reactLoadableManifest && moduleIds) {\n        const manifest = workStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const chunks = manifest[key].files;\n            allFiles.push(...chunks);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    const dplId =  false ? 0 : '';\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((chunk)=>{\n            const href = workStore.assetPrefix + \"/_next/\" + (0, _encodeuripath.encodeURIPath)(chunk) + dplId;\n            const isCss = chunk.endsWith('.css');\n            // If it's stylesheet we use `precedence` o help hoist with React Float.\n            // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n            // The `preload` for stylesheet is not optional.\n            if (isCss) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    // @ts-ignore\n                    precedence: \"dynamic\",\n                    href: href,\n                    rel: \"stylesheet\",\n                    as: \"style\"\n                }, chunk);\n            } else {\n                // If it's script we use ReactDOM.preload to preload the resources\n                (0, _reactdom.preload)(href, {\n                    as: 'script',\n                    fetchPriority: 'low'\n                });\n                return null;\n            }\n        })\n    });\n} //# sourceMappingURL=preload-chunks.js.map\n_c = PreloadChunks;\nvar _c;\n$RefreshReg$(_c, \"PreloadChunks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Dynamic imports to isolate problematic packages\nconst ReactMarkdown = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-markdown_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/index.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-markdown\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-100 rounded p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-1/2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n});\n_c = ReactMarkdown;\n// Simple fallback for syntax highlighting to avoid webpack issues\nconst SimpleSyntaxHighlighter = (param)=>{\n    let { children, language } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 22,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = SimpleSyntaxHighlighter;\nfunction MarkdownRenderer(param) {\n    let { content, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                remarkPlugins: [],\n                components: {\n                    // Headers\n                    h1: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    h2: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    h3: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    h4: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Paragraphs\n                    p: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Bold and italic\n                    strong: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-bold text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    em: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                            className: \"italic text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Lists\n                    ul: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    ol: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    li: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"leading-relaxed text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Code blocks and inline code\n                    code: (param)=>{\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || '');\n                        const language = match ? match[1] : '';\n                        const codeContent = String(children).replace(/\\n$/, '');\n                        if (!inline) {\n                            // Handle code blocks (both with and without language detection)\n                            if (language) {\n                                // Code block with simple syntax highlighting\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                text: codeContent,\n                                                variant: \"code\",\n                                                size: \"sm\",\n                                                title: \"Copy code\",\n                                                className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSyntaxHighlighter, {\n                                            language: language,\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, void 0);\n                            } else {\n                                // Code block without language (plain text code block)\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                text: codeContent,\n                                                variant: \"code\",\n                                                size: \"sm\",\n                                                title: \"Copy code\",\n                                                className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                            ...props,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, void 0);\n                    },\n                    // Blockquotes\n                    blockquote: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Links\n                    a: (param)=>{\n                        let { children, href } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: href,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Tables\n                    table: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto my-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border border-gray-200 rounded-lg\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    thead: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    tbody: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"divide-y divide-gray-200 bg-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    tr: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"hover:bg-gray-50\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    th: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                            className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    td: (param)=>{\n                        let { children } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                            className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, void 0);\n                    },\n                    // Horizontal rule\n                    hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4 border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, void 0)\n                },\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c2 = MarkdownRenderer;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ReactMarkdown\");\n$RefreshReg$(_c1, \"SimpleSyntaxHighlighter\");\n$RefreshReg$(_c2, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownRenderer.tsx\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage-instance.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDREQUEyRDtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDJCQUEyQixtQkFBTyxDQUFDLHlHQUF1QjtBQUMxRDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcd29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwid29ya0FzeW5jU3RvcmFnZUluc3RhbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3b3JrQXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfYXN5bmNsb2NhbHN0b3JhZ2UgPSByZXF1aXJlKFwiLi9hc3luYy1sb2NhbC1zdG9yYWdlXCIpO1xuY29uc3Qgd29ya0FzeW5jU3RvcmFnZUluc3RhbmNlID0gKDAsIF9hc3luY2xvY2Fsc3RvcmFnZS5jcmVhdGVBc3luY0xvY2FsU3RvcmFnZSkoKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage.external.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _workasyncstorageinstance.workAsyncStorageInstance;\n    }\n}));\nconst _workasyncstorageinstance = __webpack_require__(/*! ./work-async-storage-instance */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\");\n\n//# sourceMappingURL=work-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLG9EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLGtDQUFrQyxtQkFBTyxDQUFDLHlIQUErQjs7QUFFekUiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXHdvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndvcmtBc3luY1N0b3JhZ2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF93b3JrYXN5bmNzdG9yYWdlaW5zdGFuY2Uud29ya0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX3dvcmthc3luY3N0b3JhZ2VpbnN0YW5jZSA9IHJlcXVpcmUoXCIuL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZVwiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\n"));

/***/ })

}]);