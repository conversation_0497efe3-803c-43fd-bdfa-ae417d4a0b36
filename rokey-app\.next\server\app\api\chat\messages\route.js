/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/messages/route";
exports.ids = ["app/api/chat/messages/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fmessages%2Froute&page=%2Fapi%2Fchat%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fmessages%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fmessages%2Froute&page=%2Fapi%2Fchat%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fmessages%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_chat_messages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/messages/route.ts */ \"(rsc)/./src/app/api/chat/messages/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/messages/route\",\n        pathname: \"/api/chat/messages\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/messages/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\chat\\\\messages\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_chat_messages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fmessages%2Froute&page=%2Fapi%2Fchat%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fmessages%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/messages/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/chat/messages/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n// GET /api/chat/messages?conversation_id=<ID>&limit=<NUM>&offset=<NUM>&latest=<BOOL>\n// Retrieves messages for a specific conversation with pagination\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { searchParams } = new URL(request.url);\n    const conversationId = searchParams.get('conversation_id');\n    const limit = parseInt(searchParams.get('limit') || '50'); // Default 50 messages\n    const offset = parseInt(searchParams.get('offset') || '0');\n    const latest = searchParams.get('latest') === 'true'; // Get latest messages first\n    if (!conversationId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'conversation_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    // Validate pagination parameters\n    if (limit > 100) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Limit cannot exceed 100 messages'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        let query = supabase.from('chat_messages').select('*').eq('conversation_id', conversationId);\n        if (latest) {\n            // Get latest messages first (for initial load)\n            query = query.order('created_at', {\n                ascending: false\n            }).limit(limit);\n        } else {\n            // Get messages with pagination (for loading more)\n            query = query.order('created_at', {\n                ascending: true\n            }).range(offset, offset + limit - 1);\n        }\n        const { data: messages, error } = await query;\n        if (error) {\n            console.error('Supabase error fetching messages:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch messages',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // If getting latest messages, reverse to chronological order\n        const orderedMessages = latest && messages ? messages.reverse() : messages;\n        // Add cache headers for better performance\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(orderedMessages || [], {\n            status: 200\n        });\n        response.headers.set('Cache-Control', 'private, max-age=60'); // Cache for 1 minute\n        return response;\n    } catch (e) {\n        console.error('Error in GET /api/chat/messages:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/chat/messages\n// Creates a new chat message\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        const messageData = await request.json();\n        const { conversation_id, role, content, api_key_id, model_used, temperature_used, tokens_prompt, tokens_completion, cost } = messageData;\n        if (!conversation_id || !role || !content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: conversation_id, role, content'\n            }, {\n                status: 400\n            });\n        }\n        // Validate role\n        if (![\n            'user',\n            'assistant',\n            'system',\n            'error'\n        ].includes(role)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid role. Must be one of: user, assistant, system, error'\n            }, {\n                status: 400\n            });\n        }\n        // Validate content structure\n        if (!Array.isArray(content) || content.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Content must be a non-empty array'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await supabase.from('chat_messages').insert({\n            conversation_id,\n            role,\n            content,\n            api_key_id,\n            model_used,\n            temperature_used,\n            tokens_prompt,\n            tokens_completion,\n            cost\n        }).select().single();\n        if (error) {\n            console.error('Supabase error creating message:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create message',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Update conversation's updated_at timestamp\n        await supabase.from('chat_conversations').update({\n            updated_at: new Date().toISOString()\n        }).eq('id', conversation_id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/chat/messages:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/chat/messages?id=<ID>\n// Updates a specific message's content\nasync function PUT(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { searchParams } = new URL(request.url);\n    const messageId = searchParams.get('id');\n    if (!messageId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const updateData = await request.json();\n        const { content } = updateData;\n        if (!content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'content field is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate content structure\n        if (!Array.isArray(content) || content.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Content must be a non-empty array'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await supabase.from('chat_messages').update({\n            content\n        }).eq('id', messageId).select().single();\n        if (error) {\n            console.error('Supabase error updating message:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update message',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in PUT /api/chat/messages:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/chat/messages?id=<ID>\n// Deletes a specific message\nasync function DELETE(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { searchParams } = new URL(request.url);\n    const messageId = searchParams.get('id');\n    if (!messageId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const { error } = await supabase.from('chat_messages').delete().eq('id', messageId);\n        if (error) {\n            console.error('Supabase error deleting message:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete message',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Message deleted successfully'\n        }, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in DELETE /api/chat/messages:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/messages/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fmessages%2Froute&page=%2Fapi%2Fchat%2Fmessages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fmessages%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();