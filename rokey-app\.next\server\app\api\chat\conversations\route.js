/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/conversations/route";
exports.ids = ["app/api/chat/conversations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/conversations/route.ts */ \"(rsc)/./src/app/api/chat/conversations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/conversations/route\",\n        pathname: \"/api/chat/conversations\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/conversations/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\chat\\\\conversations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/conversations/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/conversations/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n// GET /api/chat/conversations?custom_api_config_id=<ID>\n// Retrieves all chat conversations for a specific custom_api_config_id\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in GET conversations:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view conversations.'\n        }, {\n            status: 401\n        });\n    }\n    const { searchParams } = new URL(request.url);\n    const customApiConfigId = searchParams.get('custom_api_config_id');\n    if (!customApiConfigId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'custom_api_config_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Phase 2A Optimization: Get conversations with optimized query - only fetch what we need for the list\n        const { data: conversations, error } = await supabase.from('chat_conversations').select(`\n        id,\n        custom_api_config_id,\n        title,\n        created_at,\n        updated_at\n      `).eq('custom_api_config_id', customApiConfigId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .order('updated_at', {\n            ascending: false\n        }).limit(50); // Limit to 50 most recent conversations\n        if (error) {\n            console.error('Supabase error fetching conversations:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch conversations',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Get message counts and last message previews efficiently\n        const conversationIds = (conversations || []).map((conv)=>conv.id);\n        if (conversationIds.length === 0) {\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([], {\n                status: 200\n            });\n            response.headers.set('Cache-Control', 'private, max-age=30'); // Cache for 30 seconds\n            return response;\n        }\n        // Phase 2A Optimization: Use more efficient query with proper indexing\n        const { data: messageStats, error: statsError } = await supabase.from('chat_messages').select('conversation_id, content, created_at, role').in('conversation_id', conversationIds).order('created_at', {\n            ascending: false\n        }) // Simplified ordering for better index usage\n        .limit(conversationIds.length * 2); // Limit to prevent excessive data fetching\n        if (statsError) {\n            console.warn('Error fetching message metadata:', statsError);\n        }\n        // Process message stats efficiently\n        const conversationStats = new Map();\n        if (messageStats) {\n            for (const msg of messageStats){\n                const convId = msg.conversation_id;\n                const existing = conversationStats.get(convId);\n                if (!existing) {\n                    conversationStats.set(convId, {\n                        count: 1,\n                        lastMessage: msg\n                    });\n                } else {\n                    existing.count++;\n                    // Keep the most recent message (first in our ordered result)\n                    if (!existing.lastMessage) {\n                        existing.lastMessage = msg;\n                    }\n                }\n            }\n        }\n        // Process conversations with metadata\n        const processedConversations = (conversations || []).map((conv)=>{\n            const stats = conversationStats.get(conv.id) || {\n                count: 0\n            };\n            // Get last message preview\n            let lastMessagePreview = '';\n            if (stats.lastMessage?.content && Array.isArray(stats.lastMessage.content)) {\n                const textContent = stats.lastMessage.content.filter((part)=>part.type === 'text' && part.text).map((part)=>part.text).join(' ');\n                lastMessagePreview = textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;\n            }\n            return {\n                id: conv.id,\n                custom_api_config_id: conv.custom_api_config_id,\n                title: conv.title,\n                created_at: conv.created_at,\n                updated_at: conv.updated_at,\n                message_count: stats.count,\n                last_message_preview: lastMessagePreview\n            };\n        });\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(processedConversations, {\n            status: 200\n        });\n        // Enhanced caching headers for better performance\n        const isPrefetch = request.headers.get('X-Prefetch') === 'true';\n        const cacheMaxAge = isPrefetch ? 300 : 30; // 5 minutes for prefetch, 30 seconds for regular\n        response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=60`);\n        response.headers.set('X-Content-Type-Options', 'nosniff');\n        response.headers.set('Vary', 'X-Prefetch');\n        return response;\n    } catch (e) {\n        console.error('Error in GET /api/chat/conversations:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/chat/conversations\n// Creates a new chat conversation\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in POST conversation:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create conversations.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    try {\n        const conversationData = await request.json();\n        const { custom_api_config_id, title } = conversationData;\n        if (!custom_api_config_id || !title) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: custom_api_config_id, title'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await supabase.from('chat_conversations').insert({\n            custom_api_config_id,\n            title,\n            user_id: user.id\n        }).select().single();\n        if (error) {\n            console.error('Supabase error creating conversation:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create conversation',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/chat/conversations:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/chat/conversations?id=<ID>\n// Deletes a specific conversation and all its messages\nasync function DELETE(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in DELETE conversation:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to delete conversations.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    const { searchParams } = new URL(request.url);\n    const conversationId = searchParams.get('id');\n    if (!conversationId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Delete conversation (messages will be deleted automatically due to CASCADE)\n        const { error } = await supabase.from('chat_conversations').delete().eq('id', conversationId).eq('user_id', user.id); // Filter by user_id for RLS compliance\n        if (error) {\n            console.error('Supabase error deleting conversation:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete conversation',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Conversation deleted successfully'\n        }, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in DELETE /api/chat/conversations:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/conversations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVFO0FBQ2hDO0FBR3ZDLG1FQUFtRTtBQUNuRSw2RUFBNkU7QUFDN0UsbURBQW1EO0FBQzVDLGVBQWVFO0lBQ3BCLE1BQU1DLGNBQWMsTUFBTUYscURBQU9BO0lBRWpDLE9BQU9ELGlFQUFrQkEsQ0FDdkJJLDBDQUFvQyxFQUNwQ0Esa05BQXlDLEVBQ3pDO1FBQ0VILFNBQVM7WUFDUE8sS0FBSUMsSUFBWTtnQkFDZCxPQUFPTixZQUFZSyxHQUFHLENBQUNDLE9BQU9DO1lBQ2hDO1lBQ0FDLEtBQUlGLElBQVksRUFBRUMsS0FBYSxFQUFFRSxPQUFzQjtnQkFDckQsSUFBSTtvQkFDRlQsWUFBWVEsR0FBRyxDQUFDO3dCQUFFRjt3QkFBTUM7d0JBQU8sR0FBR0UsT0FBTztvQkFBQztnQkFDNUMsRUFBRSxPQUFPQyxPQUFPO29CQUNkLDZEQUE2RDtvQkFDN0QsZ0VBQWdFO29CQUNoRSwrQ0FBK0M7b0JBQy9DQyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxzQkFBc0IsRUFBRU4sS0FBSyxtQ0FBbUMsQ0FBQyxFQUFFSTtnQkFDbkY7WUFDRjtZQUNBRyxRQUFPUCxJQUFZLEVBQUVHLE9BQXNCO2dCQUN6QyxJQUFJO29CQUNGLGlFQUFpRTtvQkFDakUsd0ZBQXdGO29CQUN4RlQsWUFBWVEsR0FBRyxDQUFDO3dCQUFFRjt3QkFBTUMsT0FBTzt3QkFBSSxHQUFHRSxPQUFPO29CQUFDO2dCQUNoRCxFQUFFLE9BQU9DLE9BQU87b0JBQ2QseURBQXlEO29CQUN6REMsUUFBUUMsSUFBSSxDQUFDLENBQUMseUJBQXlCLEVBQUVOLEtBQUssbUNBQW1DLENBQUMsRUFBRUk7Z0JBQ3RGO1lBQ0Y7UUFDRjtJQUNGO0FBRUo7QUFFQSw2RUFBNkU7QUFDdEUsU0FBU0ksc0NBQXNDQyxPQUFvQjtJQUN4RSxPQUFPbEIsaUVBQWtCQSxDQUN2QkksMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUgsU0FBUztZQUNQTyxLQUFJQyxJQUFZO2dCQUNkLE9BQU9TLFFBQVFqQixPQUFPLENBQUNPLEdBQUcsQ0FBQ0MsT0FBT0M7WUFDcEM7WUFDQUMsS0FBSUYsSUFBWSxFQUFFQyxLQUFhLEVBQUVFLE9BQXNCO1lBQ3JELDhEQUE4RDtZQUM5RCx1Q0FBdUM7WUFDekM7WUFDQUksUUFBT1AsSUFBWSxFQUFFRyxPQUFzQjtZQUN6QyxpRUFBaUU7WUFDakUsdUNBQXVDO1lBQ3pDO1FBQ0Y7SUFDRjtBQUVKIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcbGliXFxzdXBhYmFzZVxcc2VydmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNlcnZlckNsaWVudCwgdHlwZSBDb29raWVPcHRpb25zIH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XHJcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tICduZXh0L2hlYWRlcnMnO1xyXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcclxuXHJcbi8vIFRoaXMgaXMgdGhlIHN0YW5kYXJkIHNldHVwIGZvciBjcmVhdGluZyBhIFN1cGFiYXNlIHNlcnZlciBjbGllbnRcclxuLy8gaW4gTmV4dC5qcyBBcHAgUm91dGVyIChTZXJ2ZXIgQ29tcG9uZW50cywgUm91dGUgSGFuZGxlcnMsIFNlcnZlciBBY3Rpb25zKS5cclxuLy8gVXBkYXRlZCBmb3IgTmV4dC5qcyAxNSBhc3luYyBjb29raWVzIHJlcXVpcmVtZW50XHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudE9uUmVxdWVzdCgpIHtcclxuICBjb25zdCBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKTtcclxuXHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXHJcbiAgICB7XHJcbiAgICAgIGNvb2tpZXM6IHtcclxuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgICByZXR1cm4gY29va2llU3RvcmUuZ2V0KG5hbWUpPy52YWx1ZTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIHNldChuYW1lOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcsIG9wdGlvbnM6IENvb2tpZU9wdGlvbnMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldCh7IG5hbWUsIHZhbHVlLCAuLi5vcHRpb25zIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gVGhpcyBlcnJvciBjYW4gYmUgaWdub3JlZCBpZiBydW5uaW5nIGluIGEgU2VydmVyIENvbXBvbmVudFxyXG4gICAgICAgICAgICAvLyB3aGVyZSBjb29raWVzIGNhbid0IGJlIHNldCBkaXJlY3RseS4gQ29va2llIHNldHRpbmcgc2hvdWxkIGJlXHJcbiAgICAgICAgICAgIC8vIGhhbmRsZWQgaW4gU2VydmVyIEFjdGlvbnMgb3IgUm91dGUgSGFuZGxlcnMuXHJcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHNldCBjb29raWUgJyR7bmFtZX0nIChtaWdodCBiZSBpbiBhIFNlcnZlciBDb21wb25lbnQpOmAsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIHJlbW92ZShuYW1lOiBzdHJpbmcsIG9wdGlvbnM6IENvb2tpZU9wdGlvbnMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vIFRvIHJlbW92ZSBhIGNvb2tpZSB1c2luZyB0aGUgYHNldGAgbWV0aG9kIGZyb20gYG5leHQvaGVhZGVyc2AsXHJcbiAgICAgICAgICAgIC8vIHlvdSB0eXBpY2FsbHkgc2V0IGl0IHdpdGggYW4gZW1wdHkgdmFsdWUgYW5kIE1heC1BZ2U9MCBvciBhbiBleHBpcnkgZGF0ZSBpbiB0aGUgcGFzdC5cclxuICAgICAgICAgICAgY29va2llU3RvcmUuc2V0KHsgbmFtZSwgdmFsdWU6ICcnLCAuLi5vcHRpb25zIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gU2ltaWxhciB0byBzZXQsIHRoaXMgbWlnaHQgZmFpbCBpbiBhIFNlcnZlciBDb21wb25lbnQuXHJcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHJlbW92ZSBjb29raWUgJyR7bmFtZX0nIChtaWdodCBiZSBpbiBhIFNlcnZlciBDb21wb25lbnQpOmAsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfVxyXG4gICk7XHJcbn1cclxuXHJcbi8vIEFsdGVybmF0aXZlIG1ldGhvZCBmb3IgQVBJIHJvdXRlcyB0aGF0IG5lZWQgdG8gaGFuZGxlIGNvb2tpZXMgZnJvbSByZXF1ZXN0XHJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudEZyb21SZXF1ZXN0KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXHJcbiAgICB7XHJcbiAgICAgIGNvb2tpZXM6IHtcclxuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgICByZXR1cm4gcmVxdWVzdC5jb29raWVzLmdldChuYW1lKT8udmFsdWU7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBzZXQobmFtZTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nLCBvcHRpb25zOiBDb29raWVPcHRpb25zKSB7XHJcbiAgICAgICAgICAvLyBJbiBBUEkgcm91dGVzLCB3ZSBjYW4ndCBzZXQgY29va2llcyBkaXJlY3RseSBvbiB0aGUgcmVxdWVzdFxyXG4gICAgICAgICAgLy8gVGhpcyB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgfSxcclxuICAgICAgICByZW1vdmUobmFtZTogc3RyaW5nLCBvcHRpb25zOiBDb29raWVPcHRpb25zKSB7XHJcbiAgICAgICAgICAvLyBJbiBBUEkgcm91dGVzLCB3ZSBjYW4ndCByZW1vdmUgY29va2llcyBkaXJlY3RseSBvbiB0aGUgcmVxdWVzdFxyXG4gICAgICAgICAgLy8gVGhpcyB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH1cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRPblJlcXVlc3QiLCJjb29raWVTdG9yZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImdldCIsIm5hbWUiLCJ2YWx1ZSIsInNldCIsIm9wdGlvbnMiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwicmVtb3ZlIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRGcm9tUmVxdWVzdCIsInJlcXVlc3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();