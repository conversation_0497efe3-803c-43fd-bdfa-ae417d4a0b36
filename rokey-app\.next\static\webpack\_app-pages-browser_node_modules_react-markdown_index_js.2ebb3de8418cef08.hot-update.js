"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_node_modules_react-markdown_index_js",{

/***/ "(app-pages-browser)/./src/lib/debug-fallback.js":
/*!***********************************!*\
  !*** ./src/lib/debug-fallback.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Debug package fallback for browser environments\n// This prevents the webpack error when debug package is imported\n\nfunction createDebug() {\n    function debug() {\n    // No-op in browser\n    }\n    debug.enabled = false;\n    debug.namespace = '';\n    debug.extend = ()=>debug;\n    debug.destroy = ()=>{};\n    debug.log = ()=>{};\n    debug.call = ()=>{}; // Add the missing call property\n    return debug;\n}\n// Export both default and named exports to match debug package API\nconst debug = createDebug();\ndebug.default = debug;\ndebug.call = ()=>{}; // Add call property to the main debug object too\nmodule.exports = debug;\nmodule.exports[\"default\"] = debug;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZGVidWctZmFsbGJhY2suanMiLCJtYXBwaW5ncyI6IkFBQUEsa0RBQWtEO0FBQ2xELGlFQUFpRTs7QUFFakUsU0FBU0E7SUFDUCxTQUFTQztJQUNQLG1CQUFtQjtJQUNyQjtJQUVBQSxNQUFNQyxPQUFPLEdBQUc7SUFDaEJELE1BQU1FLFNBQVMsR0FBRztJQUNsQkYsTUFBTUcsTUFBTSxHQUFHLElBQU1IO0lBQ3JCQSxNQUFNSSxPQUFPLEdBQUcsS0FBTztJQUN2QkosTUFBTUssR0FBRyxHQUFHLEtBQU87SUFDbkJMLE1BQU1NLElBQUksR0FBRyxLQUFPLEdBQUcsZ0NBQWdDO0lBRXZELE9BQU9OO0FBQ1Q7QUFFQSxtRUFBbUU7QUFDbkUsTUFBTUEsUUFBUUQ7QUFDZEMsTUFBTU8sT0FBTyxHQUFHUDtBQUNoQkEsTUFBTU0sSUFBSSxHQUFHLEtBQU8sR0FBRyxpREFBaUQ7QUFFeEVFLE9BQU9DLE9BQU8sR0FBR1Q7QUFDakJRLHlCQUFzQixHQUFHUiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGxpYlxcZGVidWctZmFsbGJhY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRGVidWcgcGFja2FnZSBmYWxsYmFjayBmb3IgYnJvd3NlciBlbnZpcm9ubWVudHNcbi8vIFRoaXMgcHJldmVudHMgdGhlIHdlYnBhY2sgZXJyb3Igd2hlbiBkZWJ1ZyBwYWNrYWdlIGlzIGltcG9ydGVkXG5cbmZ1bmN0aW9uIGNyZWF0ZURlYnVnKCkge1xuICBmdW5jdGlvbiBkZWJ1ZygpIHtcbiAgICAvLyBOby1vcCBpbiBicm93c2VyXG4gIH1cblxuICBkZWJ1Zy5lbmFibGVkID0gZmFsc2U7XG4gIGRlYnVnLm5hbWVzcGFjZSA9ICcnO1xuICBkZWJ1Zy5leHRlbmQgPSAoKSA9PiBkZWJ1ZztcbiAgZGVidWcuZGVzdHJveSA9ICgpID0+IHt9O1xuICBkZWJ1Zy5sb2cgPSAoKSA9PiB7fTtcbiAgZGVidWcuY2FsbCA9ICgpID0+IHt9OyAvLyBBZGQgdGhlIG1pc3NpbmcgY2FsbCBwcm9wZXJ0eVxuXG4gIHJldHVybiBkZWJ1Zztcbn1cblxuLy8gRXhwb3J0IGJvdGggZGVmYXVsdCBhbmQgbmFtZWQgZXhwb3J0cyB0byBtYXRjaCBkZWJ1ZyBwYWNrYWdlIEFQSVxuY29uc3QgZGVidWcgPSBjcmVhdGVEZWJ1ZygpO1xuZGVidWcuZGVmYXVsdCA9IGRlYnVnO1xuZGVidWcuY2FsbCA9ICgpID0+IHt9OyAvLyBBZGQgY2FsbCBwcm9wZXJ0eSB0byB0aGUgbWFpbiBkZWJ1ZyBvYmplY3QgdG9vXG5cbm1vZHVsZS5leHBvcnRzID0gZGVidWc7XG5tb2R1bGUuZXhwb3J0cy5kZWZhdWx0ID0gZGVidWc7XG4iXSwibmFtZXMiOlsiY3JlYXRlRGVidWciLCJkZWJ1ZyIsImVuYWJsZWQiLCJuYW1lc3BhY2UiLCJleHRlbmQiLCJkZXN0cm95IiwibG9nIiwiY2FsbCIsImRlZmF1bHQiLCJtb2R1bGUiLCJleHBvcnRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/debug-fallback.js\n"));

/***/ })

});