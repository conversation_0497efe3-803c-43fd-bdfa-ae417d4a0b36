import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { type NewChatConversation, type ChatConversation } from '@/types/chatHistory';

// GET /api/chat/conversations?custom_api_config_id=<ID>
// Retrieves all chat conversations for a specific custom_api_config_id
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user (more secure than getSession)
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication error in GET conversations:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to view conversations.' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const customApiConfigId = searchParams.get('custom_api_config_id');

  if (!customApiConfigId) {
    return NextResponse.json({ error: 'custom_api_config_id query parameter is required' }, { status: 400 });
  }

  try {
    // Phase 2A Optimization: Get conversations with optimized query - only fetch what we need for the list
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select(`
        id,
        custom_api_config_id,
        title,
        created_at,
        updated_at
      `)
      .eq('custom_api_config_id', customApiConfigId)
      .eq('user_id', user.id) // Filter by user_id for RLS compliance
      .order('updated_at', { ascending: false })
      .limit(50); // Limit to 50 most recent conversations

    if (error) {
      console.error('Supabase error fetching conversations:', error);
      return NextResponse.json({ error: 'Failed to fetch conversations', details: error.message }, { status: 500 });
    }

    // Get message counts and last message previews efficiently
    const conversationIds = (conversations || []).map(conv => conv.id);

    if (conversationIds.length === 0) {
      const response = NextResponse.json([], { status: 200 });
      response.headers.set('Cache-Control', 'private, max-age=30'); // Cache for 30 seconds
      return response;
    }

    // Phase 2A Optimization: Use more efficient query with proper indexing
    const { data: messageStats, error: statsError } = await supabase
      .from('chat_messages')
      .select('conversation_id, content, created_at, role')
      .in('conversation_id', conversationIds)
      .order('created_at', { ascending: false }) // Simplified ordering for better index usage
      .limit(conversationIds.length * 2); // Limit to prevent excessive data fetching

    if (statsError) {
      console.warn('Error fetching message metadata:', statsError);
    }

    // Process message stats efficiently
    const conversationStats = new Map<string, { count: number, lastMessage?: any }>();

    if (messageStats) {
      for (const msg of messageStats) {
        const convId = msg.conversation_id;
        const existing = conversationStats.get(convId);

        if (!existing) {
          conversationStats.set(convId, { count: 1, lastMessage: msg });
        } else {
          existing.count++;
          // Keep the most recent message (first in our ordered result)
          if (!existing.lastMessage) {
            existing.lastMessage = msg;
          }
        }
      }
    }

    // Process conversations with metadata
    const processedConversations: ChatConversation[] = (conversations || []).map(conv => {
      const stats = conversationStats.get(conv.id) || { count: 0 };

      // Get last message preview
      let lastMessagePreview = '';
      if (stats.lastMessage?.content && Array.isArray(stats.lastMessage.content)) {
        const textContent = stats.lastMessage.content
          .filter((part: any) => part.type === 'text' && part.text)
          .map((part: any) => part.text)
          .join(' ');
        lastMessagePreview = textContent.length > 100
          ? textContent.substring(0, 100) + '...'
          : textContent;
      }

      return {
        id: conv.id,
        custom_api_config_id: conv.custom_api_config_id,
        title: conv.title,
        created_at: conv.created_at,
        updated_at: conv.updated_at,
        message_count: stats.count,
        last_message_preview: lastMessagePreview
      };
    });

    const response = NextResponse.json(processedConversations, { status: 200 });

    // Enhanced caching headers for better performance
    const isPrefetch = request.headers.get('X-Prefetch') === 'true';
    const cacheMaxAge = isPrefetch ? 300 : 30; // 5 minutes for prefetch, 30 seconds for regular

    response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=60`);
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Vary', 'X-Prefetch');

    return response;

  } catch (e: any) {
    console.error('Error in GET /api/chat/conversations:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// POST /api/chat/conversations
// Creates a new chat conversation
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user from session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !session?.user) {
    console.error('Authentication error in POST conversation:', sessionError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to create conversations.' }, { status: 401 });
  }

  const user = session.user;

  try {
    const conversationData = await request.json() as NewChatConversation;
    const { custom_api_config_id, title } = conversationData;

    if (!custom_api_config_id || !title) {
      return NextResponse.json({ error: 'Missing required fields: custom_api_config_id, title' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('chat_conversations')
      .insert({
        custom_api_config_id,
        title,
        user_id: user.id, // Add user_id for RLS compliance
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating conversation:', error);
      return NextResponse.json({ error: 'Failed to create conversation', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 201 });

  } catch (e: any) {
    console.error('Error in POST /api/chat/conversations:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// DELETE /api/chat/conversations?id=<ID>
// Deletes a specific conversation and all its messages
export async function DELETE(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user from session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !session?.user) {
    console.error('Authentication error in DELETE conversation:', sessionError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to delete conversations.' }, { status: 401 });
  }

  const user = session.user;

  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get('id');

  if (!conversationId) {
    return NextResponse.json({ error: 'id query parameter is required' }, { status: 400 });
  }

  try {
    // Delete conversation (messages will be deleted automatically due to CASCADE)
    const { error } = await supabase
      .from('chat_conversations')
      .delete()
      .eq('id', conversationId)
      .eq('user_id', user.id); // Filter by user_id for RLS compliance

    if (error) {
      console.error('Supabase error deleting conversation:', error);
      return NextResponse.json({ error: 'Failed to delete conversation', details: error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Conversation deleted successfully' }, { status: 200 });

  } catch (e: any) {
    console.error('Error in DELETE /api/chat/conversations:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}
