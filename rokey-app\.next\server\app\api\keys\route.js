/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/keys/route";
exports.ids = ["app/api/keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/keys/route.ts */ \"(rsc)/./src/app/api/keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/keys/route\",\n        pathname: \"/api/keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/keys/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/keys/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n\n// cookies function is not directly needed here if createSupabaseServerClientOnRequest handles it\n\n // encrypt returns a single string now\n // Import Node.js crypto module\n// POST /api/keys\n// Adds a new API key to a specific custom_api_config\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in POST /api/keys:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create API keys.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const keyData = await request.json();\n        const { custom_api_config_id, provider, predefined_model_id, api_key_raw, label, temperature = 1.0 } = keyData;\n        if (!custom_api_config_id || !provider || !predefined_model_id || !api_key_raw || !label) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label'\n            }, {\n                status: 400\n            });\n        }\n        // Validate temperature\n        if (temperature < 0.0 || temperature > 2.0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Temperature must be between 0.0 and 2.0'\n            }, {\n                status: 400\n            });\n        }\n        if (typeof api_key_raw !== 'string' || api_key_raw.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key cannot be empty.'\n            }, {\n                status: 400\n            });\n        }\n        // ROKEY_ENCRYPTION_KEY is checked within encryption.ts now, but good to be aware of its necessity.\n        // const encryptionKey = process.env.ROKEY_ENCRYPTION_KEY;\n        // if (!encryptionKey) {\n        //   console.error('ROKEY_ENCRYPTION_KEY is not set.');\n        //   return NextResponse.json({ error: 'Server configuration error: Encryption key not found.' }, { status: 500 });\n        // }\n        // encrypt function now returns a single string: iv:authTag:encryptedData\n        const encrypted_api_key_combined = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_2__.encrypt)(api_key_raw);\n        const api_key_hash = crypto__WEBPACK_IMPORTED_MODULE_3___default().createHash('sha256').update(api_key_raw).digest('hex');\n        // The type Omit<ApiKey, ...> will align because ApiKey no longer has 'iv'\n        const newDbKey = {\n            custom_api_config_id,\n            provider,\n            predefined_model_id,\n            encrypted_api_key: encrypted_api_key_combined,\n            label,\n            api_key_hash,\n            status: 'active',\n            is_default_general_chat_model: false,\n            temperature,\n            user_id: user.id\n        };\n        const { data, error } = await supabase.from('api_keys').insert(newDbKey).select().single();\n        if (error) {\n            console.error('Supabase error creating API key:', error);\n            if (error.code === '23503') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid custom_api_config_id or predefined_model_id.',\n                    details: error.message\n                }, {\n                    status: 400\n                });\n            }\n            if (error.code === '23505') {\n                // Check if this is the new unique_model_per_config constraint\n                if (error.message.includes('unique_model_per_config')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.',\n                        details: error.message\n                    }, {\n                        status: 409\n                    });\n                }\n                // Fallback for other unique constraint violations\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'A unique constraint was violated.',\n                    details: error.message\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to save API key',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // If this is the first key for the config, or no other key is default, make this one default.\n        if (data) {\n            const { data: existingDefaults, error: defaultCheckError } = await supabase.from('api_keys').select('id').eq('custom_api_config_id', custom_api_config_id).eq('user_id', user.id) // Filter by user_id for RLS compliance\n            .eq('is_default_general_chat_model', true).neq('id', data.id) // Exclude the newly added key itself from this check\n            .limit(1);\n            if (defaultCheckError) {\n                console.error('Error checking for existing default keys:', defaultCheckError);\n            // Proceed without making it default, but log the error. The key is still saved.\n            } else if (!existingDefaults || existingDefaults.length === 0) {\n                // No other key is default, so make this new one default\n                const { data: updatedKey, error: updateError } = await supabase.from('api_keys').update({\n                    is_default_general_chat_model: true\n                }).eq('id', data.id).eq('user_id', user.id) // Filter by user_id for RLS compliance\n                .select().single();\n                if (updateError) {\n                    console.error('Error updating new key to be default:', updateError);\n                // Key is saved, but failed to make it default. Log and proceed.\n                } else {\n                    // Successfully made the new key default, return this updated key data\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedKey, {\n                        status: 201\n                    });\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/keys:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        // Catch errors from encrypt function (e.g., if ROKEY_ENCRYPTION_KEY was invalid)\n        if (e.message.includes('Invalid ROKEY_ENCRYPTION_KEY') || e.message.includes('Encryption input must be a non-empty string')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server-side encryption error',\n                details: e.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/keys?custom_config_id=<ID>\n// Retrieves all API keys for a specific custom_api_config_id\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in GET /api/keys:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const { searchParams } = new URL(request.url);\n    const customConfigId = searchParams.get('custom_config_id');\n    if (!customConfigId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'custom_config_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const { data: keys, error } = await supabase// Ensure selected fields match DisplayApiKey definition\n        .from('api_keys').select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model').eq('custom_api_config_id', customConfigId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Supabase error fetching API keys:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch API keys',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        const displayKeys = (keys || []).map((key)=>({\n                id: key.id,\n                custom_api_config_id: key.custom_api_config_id,\n                provider: key.provider,\n                predefined_model_id: key.predefined_model_id,\n                label: key.label,\n                status: key.status,\n                temperature: key.temperature,\n                created_at: key.created_at,\n                last_used_at: key.last_used_at,\n                is_default_general_chat_model: key.is_default_general_chat_model\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(displayKeys, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in GET /api/keys:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/keys?id=<ID>\n// Updates an existing API key (currently supports temperature updates)\nasync function PUT(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in PUT /api/keys:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to update API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    const { searchParams } = new URL(request.url);\n    const keyId = searchParams.get('id');\n    if (!keyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const updateData = await request.json();\n        const { temperature, predefined_model_id } = updateData;\n        // Prepare update object\n        const updateFields = {};\n        if (temperature !== undefined) {\n            // Validate temperature\n            if (temperature < 0.0 || temperature > 2.0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Temperature must be between 0.0 and 2.0'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.temperature = temperature;\n        }\n        if (predefined_model_id !== undefined) {\n            if (typeof predefined_model_id !== 'string' || predefined_model_id.trim().length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Model ID must be a non-empty string'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.predefined_model_id = predefined_model_id;\n        }\n        // If no fields to update, return error\n        if (Object.keys(updateFields).length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No valid fields provided for update'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await supabase.from('api_keys').update(updateFields).eq('id', keyId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model').single();\n        if (error) {\n            console.error('Supabase error updating API key:', error);\n            if (error.code === '23505' && error.message.includes('unique_model_per_config')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'This model is already configured in this setup. Each model can only be used once per configuration.',\n                    details: error.message\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update API key',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in PUT /api/keys:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ALGORITHM = 'aes-256-gcm';\nconst IV_LENGTH = 12; // Recommended for GCM\nconst AUTH_TAG_LENGTH = 16; // GCM produces a 16-byte auth tag\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\nconst key = Buffer.from(ROKEY_ENCRYPTION_KEY_FROM_ENV, 'hex');\nfunction encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(IV_LENGTH);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipheriv(ALGORITHM, key, iv);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    const authTag = cipher.getAuthTag();\n    // Prepend IV and authTag to the encrypted text (hex encoded)\n    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;\n}\nfunction decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = Buffer.from(parts[0], 'hex');\n    const authTag = Buffer.from(parts[1], 'hex');\n    const encryptedData = parts[2];\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== AUTH_TAG_LENGTH) {\n        throw new Error(`Invalid authTag length. Expected ${AUTH_TAG_LENGTH} bytes.`);\n    }\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipheriv(ALGORITHM, key, iv);\n    decipher.setAuthTag(authTag);\n    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();