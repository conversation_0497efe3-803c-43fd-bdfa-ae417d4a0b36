"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_2__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n}\n// Dynamic imports to isolate problematic packages with error handling\nconst ReactMarkdown = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-markdown\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-100 rounded p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-1/2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n});\n// Simple fallback for syntax highlighting to avoid webpack issues\nconst SimpleSyntaxHighlighter = ({ children, language })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined);\nfunction MarkdownRenderer({ content, className = '' }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `markdown-content ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                    remarkPlugins: [],\n                    components: {\n                        // Headers\n                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, void 0),\n                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, void 0),\n                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, void 0),\n                        h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, void 0),\n                        // Paragraphs\n                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, void 0),\n                        // Bold and italic\n                        strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, void 0),\n                        em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, void 0),\n                        // Lists\n                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, void 0),\n                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, void 0),\n                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, void 0),\n                        // Code blocks and inline code\n                        code: ({ node, inline, className, children, ...props })=>{\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Handle code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with simple syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSyntaxHighlighter, {\n                                                language: language,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, void 0),\n                        // Links\n                        a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, void 0),\n                        // Tables\n                        table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, void 0),\n                        thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, void 0),\n                        tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, void 0),\n                        tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, void 0),\n                        th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, void 0),\n                        td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, void 0),\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;