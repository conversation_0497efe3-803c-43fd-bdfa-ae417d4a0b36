"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Dynamic imports to isolate problematic packages\nconst ReactMarkdown = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-markdown\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-100 rounded p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-1/2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n});\nconst remarkGfm = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"remark-gfm\"\n        ]\n    },\n    ssr: false\n});\n// Simple fallback for syntax highlighting to avoid webpack issues\nconst SimpleSyntaxHighlighter = ({ children, language })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 24,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined);\nfunction MarkdownRenderer({ content, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                remarkPlugins: remarkGfm ? [\n                    remarkGfm\n                ] : [],\n                components: {\n                    // Headers\n                    h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, void 0),\n                    h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, void 0),\n                    h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, void 0),\n                    h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, void 0),\n                    // Paragraphs\n                    p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, void 0),\n                    // Bold and italic\n                    strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-bold text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, void 0),\n                    em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                            className: \"italic text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, void 0),\n                    // Lists\n                    ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, void 0),\n                    ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, void 0),\n                    li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"leading-relaxed text-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, void 0),\n                    // Code blocks and inline code\n                    code: ({ node, inline, className, children, ...props })=>{\n                        const match = /language-(\\w+)/.exec(className || '');\n                        const language = match ? match[1] : '';\n                        const codeContent = String(children).replace(/\\n$/, '');\n                        if (!inline) {\n                            // Handle code blocks (both with and without language detection)\n                            if (language) {\n                                // Code block with simple syntax highlighting\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                text: codeContent,\n                                                variant: \"code\",\n                                                size: \"sm\",\n                                                title: \"Copy code\",\n                                                className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSyntaxHighlighter, {\n                                            language: language,\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, void 0);\n                            } else {\n                                // Code block without language (plain text code block)\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                text: codeContent,\n                                                variant: \"code\",\n                                                size: \"sm\",\n                                                title: \"Copy code\",\n                                                className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                            ...props,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, void 0);\n                    },\n                    // Blockquotes\n                    blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, void 0),\n                    // Links\n                    a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: href,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, void 0),\n                    // Tables\n                    table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto my-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border border-gray-200 rounded-lg\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, void 0),\n                    thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, void 0),\n                    tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"divide-y divide-gray-200 bg-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, void 0),\n                    tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"hover:bg-gray-50\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, void 0),\n                    th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                            className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, void 0),\n                    td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                            className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, void 0),\n                    // Horizontal rule\n                    hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4 border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, void 0)\n                },\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;