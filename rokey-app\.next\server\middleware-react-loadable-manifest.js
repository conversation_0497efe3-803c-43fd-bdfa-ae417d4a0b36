self.__REACT_LOADABLE_MANIFEST="{\"..\\\\node_modules\\\\@supabase\\\\auth-js\\\\dist\\\\module\\\\lib\\\\helpers.js -> @supabase/node-fetch\":{\"id\":\"..\\\\node_modules\\\\@supabase\\\\auth-js\\\\dist\\\\module\\\\lib\\\\helpers.js -> @supabase/node-fetch\",\"files\":[]},\"..\\\\node_modules\\\\@supabase\\\\functions-js\\\\dist\\\\module\\\\helper.js -> @supabase/node-fetch\":{\"id\":\"..\\\\node_modules\\\\@supabase\\\\functions-js\\\\dist\\\\module\\\\helper.js -> @supabase/node-fetch\",\"files\":[]},\"..\\\\node_modules\\\\@supabase\\\\realtime-js\\\\dist\\\\module\\\\RealtimeClient.js -> @supabase/node-fetch\":{\"id\":\"..\\\\node_modules\\\\@supabase\\\\realtime-js\\\\dist\\\\module\\\\RealtimeClient.js -> @supabase/node-fetch\",\"files\":[]},\"..\\\\node_modules\\\\@supabase\\\\storage-js\\\\dist\\\\module\\\\lib\\\\helpers.js -> @supabase/node-fetch\":{\"id\":\"..\\\\node_modules\\\\@supabase\\\\storage-js\\\\dist\\\\module\\\\lib\\\\helpers.js -> @supabase/node-fetch\",\"files\":[]},\"..\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\utils\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\":{\"id\":\"..\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\utils\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js\"]},\"app\\\\playground\\\\page.tsx -> @/components/OrchestrationCanvas\":{\"id\":\"app\\\\playground\\\\page.tsx -> @/components/OrchestrationCanvas\",\"files\":[\"static/chunks/_app-pages-browser_src_components_OrchestrationCanvas_tsx.js\"]},\"components\\\\LazyMarkdownRenderer.tsx -> ./MarkdownRenderer\":{\"id\":\"components\\\\LazyMarkdownRenderer.tsx -> ./MarkdownRenderer\",\"files\":[\"static/chunks/_app-pages-browser_src_components_MarkdownRenderer_tsx.js\"]},\"components\\\\MarkdownRenderer.tsx -> react-markdown\":{\"id\":\"components\\\\MarkdownRenderer.tsx -> react-markdown\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_react-markdown_index_js.js\"]},\"components\\\\MarkdownRenderer.tsx -> remark-gfm\":{\"id\":\"components\\\\MarkdownRenderer.tsx -> remark-gfm\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_remark-gfm_index_js.js\"]}}"