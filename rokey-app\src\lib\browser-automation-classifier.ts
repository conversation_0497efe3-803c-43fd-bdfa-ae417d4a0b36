/**
 * Browser Automation Classifier
 * Detects when user requests require browser automation and classifies task types
 */

export interface BrowserAutomationClassification {
  requiresBrowserAutomation: boolean;
  taskType: 'navigation' | 'data_extraction' | 'form_filling' | 'verification' | 'monitoring' | 'comparison' | 'automation';
  confidence: number;
  extractedParameters: {
    url?: string;
    website?: string;
    searchQuery?: string;
    dataToExtract?: string;
    formData?: Record<string, any>;
    verificationCriteria?: string;
  };
  reasoning: string;
}

export class BrowserAutomationClassifier {
  // REMOVED: No keyword arrays - all classification handled by Gemini

  private static readonly URL_PATTERNS = [
    /https?:\/\/[^\s]+/gi,
    /www\.[^\s]+/gi,
    /[a-zA-Z0-9-]+\.(com|org|net|edu|gov|io|co|uk|de|fr|jp|cn|au|ca)[^\s]*/gi
  ];

  private static readonly WEBSITE_NAMES = [
    'google', 'amazon', 'ebay', 'facebook', 'twitter', 'linkedin',
    'youtube', 'instagram', 'reddit', 'wikipedia', 'github',
    'stackoverflow', 'medium', 'netflix', 'spotify', 'airbnb',
    'uber', 'lyft', 'booking', 'expedia', 'tripadvisor'
  ];

  public static async classifyMessage(
    message: string,
    conversationContext: string[] = [],
    classificationApiKey?: string
  ): Promise<BrowserAutomationClassification> {
    // ALWAYS use Gemini classification first - NO keyword matching!
    if (!classificationApiKey) {
      throw new Error('Classification API key is required for browser automation detection');
    }

    try {
      // Primary: Gemini-based classification
      const geminiResult = await this.geminiClassification(message, conversationContext, classificationApiKey);

      if (!geminiResult.requiresBrowserAutomation) {
        return {
          requiresBrowserAutomation: false,
          taskType: 'navigation',
          confidence: geminiResult.confidence,
          extractedParameters: {},
          reasoning: 'Gemini classified as non-browser task'
        };
      }

      // Extract URLs and websites for parameter extraction only (not classification)
      const urlMatches = this.extractUrls(message);
      const websiteMatches = this.detectWebsiteNames(message.toLowerCase());

      // Get task type from Gemini
      const taskType = await this.geminiTaskTypeClassification(message, conversationContext, classificationApiKey);

      // Extract parameters
      const extractedParameters = this.extractParameters(message, message.toLowerCase(), urlMatches, websiteMatches);

      return {
        requiresBrowserAutomation: true,
        taskType,
        confidence: geminiResult.confidence,
        extractedParameters,
        reasoning: `Gemini classified as browser automation task: ${taskType}`
      };

    } catch (error) {
      console.error('Gemini classification failed:', error);
      throw new Error('Browser automation classification failed - Gemini API unavailable');
    }
  }

  // REMOVED: No more keyword scoring - Gemini handles all classification

  private static extractUrls(message: string): string[] {
    const urls: string[] = [];
    
    for (const pattern of this.URL_PATTERNS) {
      const matches = message.match(pattern);
      if (matches) {
        urls.push(...matches);
      }
    }
    
    return [...new Set(urls)]; // Remove duplicates
  }

  private static detectWebsiteNames(messageText: string): string[] {
    const detected: string[] = [];
    
    for (const website of this.WEBSITE_NAMES) {
      if (messageText.includes(website)) {
        detected.push(website);
      }
    }
    
    return detected;
  }

  // REMOVED: Task type classification now handled by Gemini

  private static extractParameters(
    originalMessage: string,
    messageText: string,
    urlMatches: string[],
    websiteMatches: string[]
  ): BrowserAutomationClassification['extractedParameters'] {
    const parameters: BrowserAutomationClassification['extractedParameters'] = {};
    
    // Extract URLs
    if (urlMatches.length > 0) {
      parameters.url = urlMatches[0];
    }
    
    // Extract website names
    if (websiteMatches.length > 0) {
      parameters.website = websiteMatches[0];
    }
    
    // Extract search queries (simple heuristic)
    const searchPatterns = [
      /search for "([^"]+)"/i,
      /search for ([^.!?]+)/i,
      /find "([^"]+)"/i,
      /look up "([^"]+)"/i,
      /google "([^"]+)"/i
    ];
    
    for (const pattern of searchPatterns) {
      const match = originalMessage.match(pattern);
      if (match) {
        parameters.searchQuery = match[1].trim();
        break;
      }
    }
    
    // Extract data extraction targets
    const extractPatterns = [
      /extract "([^"]+)"/i,
      /get the ([^.!?]+)/i,
      /find the ([^.!?]+)/i,
      /scrape ([^.!?]+)/i
    ];
    
    for (const pattern of extractPatterns) {
      const match = originalMessage.match(pattern);
      if (match) {
        parameters.dataToExtract = match[1].trim();
        break;
      }
    }
    
    return parameters;
  }

  // REMOVED: Reasoning now generated by Gemini classification

  private static async geminiClassification(
    message: string,
    conversationContext: string[],
    apiKey: string
  ): Promise<{ requiresBrowserAutomation: boolean; confidence: number }> {
    const systemPrompt = `You are RouKey's intelligent browser automation classifier. Determine if the user's request requires browser automation (web browsing, data extraction, form filling, etc.).

Respond with JSON: {"requiresBrowserAutomation": boolean, "confidence": number}

Examples that require browser automation:
- "Check the price of iPhone on Amazon"
- "Fill out this form on the website"
- "Search Google for recent news about AI"
- "Extract data from this webpage"
- "Compare prices between different sites"
- "Navigate to example.com and click the login button"
- "Find the cheapest laptop on Best Buy"
- "Monitor this website for changes"

Examples that don't require browser automation:
- "Write a story about cats"
- "Solve this math problem"
- "Explain quantum physics"
- "Generate code for a function"
- "What's the weather like?" (unless specific website mentioned)
- "Help me write an email"`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-lite',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Context: ${conversationContext.join(' ')}\n\nCurrent request: ${message}` }
        ],
        temperature: 0.1,
        max_tokens: 100
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices?.[0]?.message?.content;

    if (content) {
      // Handle Gemini responses that may be wrapped in markdown code blocks
      let cleanContent = content.trim();

      // Remove markdown code block formatting if present
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      try {
        const parsed = JSON.parse(cleanContent);
        return {
          requiresBrowserAutomation: parsed.requiresBrowserAutomation || false,
          confidence: parsed.confidence || 0.5
        };
      } catch (parseError) {
        console.error('Failed to parse Gemini classification response:', cleanContent);
        throw new Error(`Invalid JSON response from Gemini: ${parseError.message}`);
      }
    }

    throw new Error('Invalid Gemini response format');
  }

  private static async geminiTaskTypeClassification(
    message: string,
    conversationContext: string[],
    apiKey: string
  ): Promise<BrowserAutomationClassification['taskType']> {
    const systemPrompt = `You are RouKey's task type classifier. Classify the browser automation task type.

Respond with JSON: {"taskType": "navigation" | "data_extraction" | "form_filling" | "verification" | "monitoring" | "comparison" | "automation"}

Task Types:
- navigation: Simple browsing, visiting pages
- data_extraction: Getting information, prices, content from websites
- form_filling: Filling out forms, signing up, submitting data
- verification: Checking if something exists, validating information
- monitoring: Watching for changes, tracking updates
- comparison: Comparing prices, products, options across sites
- automation: Complex multi-step workflows`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-lite',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Context: ${conversationContext.join(' ')}\n\nTask: ${message}` }
        ],
        temperature: 0.1,
        max_tokens: 50
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini task type API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices?.[0]?.message?.content;

    if (content) {
      // Handle Gemini responses that may be wrapped in markdown code blocks
      let cleanContent = content.trim();

      // Remove markdown code block formatting if present
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      try {
        const parsed = JSON.parse(cleanContent);
        return parsed.taskType || 'navigation';
      } catch (parseError) {
        console.error('Failed to parse Gemini task type response:', cleanContent);
        return 'navigation'; // Safe fallback
      }
    }

    return 'navigation'; // Safe fallback
  }
}
